"""
Word文档管理 MCP服务主程序

提供Word文档的基础管理功能，包括创建、复制、信息获取等操作。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    create_document,
    copy_document,
    get_document_info,
    get_document_text,
    list_available_documents
)


def get_transport_config():
    """
    从环境变量获取传输配置
    
    Returns:
        dict: 包含传输类型、主机、端口等设置的传输配置
    """
    # 默认配置
    config = {
        'transport': 'stdio',  # 默认使用stdio以保持向后兼容
        'host': '0.0.0.0',
        'port': 8001,
        'path': '/mcp',
        'sse_path': '/sse'
    }
    
    # 使用环境变量覆盖默认配置
    transport = os.getenv('MCP_TRANSPORT', 'stdio').lower()
    print(f"传输方式: {transport}")
    
    # 验证传输类型
    valid_transports = ['stdio', 'streamable-http', 'sse']
    if transport not in valid_transports:
        print(f"警告: 无效的传输方式 '{transport}'。回退到 'stdio'。")
        transport = 'stdio'
    
    config['transport'] = transport
    config['host'] = os.getenv('MCP_HOST', config['host'])
    config['port'] = int(os.getenv('MCP_PORT', config['port']))
    config['path'] = os.getenv('MCP_PATH', config['path'])
    config['sse_path'] = os.getenv('MCP_SSE_PATH', config['sse_path'])
    
    return config


# 初始化FastMCP服务器
mcp = FastMCP("Word文档管理服务")


def register_tools():
    """使用FastMCP装饰器注册所有工具"""
    
    @mcp.tool()
    def create_document_tool(filename: str, title: str = None, author: str = None):
        """创建新的Word文档，可选择设置元数据"""
        return create_document(filename, title, author)
    
    @mcp.tool()
    def copy_document_tool(source_filename: str, destination_filename: str = None):
        """创建Word文档的副本"""
        return copy_document(source_filename, destination_filename)
    
    @mcp.tool()
    def get_document_info_tool(filename: str):
        """获取Word文档的信息"""
        return get_document_info(filename)
    
    @mcp.tool()
    def get_document_text_tool(filename: str):
        """从Word文档中提取所有文本"""
        return get_document_text(filename)
    
    @mcp.tool()
    def list_available_documents_tool(directory: str = "."):
        """列出指定目录中的所有.docx文件"""
        return list_available_documents(directory)


def run_server():
    """运行Word文档管理MCP服务器，支持可配置的传输方式"""
    # 获取传输配置
    config = get_transport_config()
    
    # 注册所有工具
    register_tools()
    
    # 打印启动信息
    transport_type = config['transport']
    print(f"启动Word文档管理MCP服务器，使用 {transport_type} 传输方式...")
    
    try:
        if transport_type == 'stdio':
            # 使用stdio传输运行（默认，向后兼容）
            print("服务器运行在stdio传输方式")
            mcp.run(transport='stdio')
            
        elif transport_type == 'streamable-http':
            # 使用streamable HTTP传输运行
            print(f"服务器运行在streamable-http传输方式，地址: http://{config['host']}:{config['port']}{config['path']}")
            mcp.run(
                transport='streamable-http',
                host=config['host'],
                port=config['port'],
                path=config['path']
            )
            
        elif transport_type == 'sse':
            # 使用SSE传输运行
            print(f"服务器运行在SSE传输方式，地址: http://{config['host']}:{config['port']}{config['sse_path']}")
            mcp.run(
                transport='sse',
                host=config['host'],
                port=config['port'],
                path=config['sse_path']
            )
            
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)
    
    return mcp


def main():
    """服务器的主入口点"""
    run_server()


if __name__ == "__main__":
    main()
