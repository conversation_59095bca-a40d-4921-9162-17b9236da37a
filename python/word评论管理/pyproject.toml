[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-comment-management-mcp"
version = "1.0.0"
description = "Word评论管理MCP服务 - 提供评论获取和管理功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
    "lxml>=4.9.0",
]

[project.scripts]
word-comment-management = "word_comment_management.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_comment_management*"]
