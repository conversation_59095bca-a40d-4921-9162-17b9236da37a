# word评论管理 MCP服务

Word评论管理MCP服务 - 提供评论获取和管理功能

## 功能特性

### 🔧 工具列表 (3个)
- `get_all_comments - 提取文档中的所有评论`
- `get_comments_by_author - 按作者获取评论`
- `get_comments_for_paragraph - 获取特定段落的评论`

## 安装和配置

### 本地安装
```bash
cd python/word评论管理
pip install -e .
```

### Claude <PERSON>配置
```json
{
  "mcpServers": {
    "word-comment-management": {
      "command": "python",
      "args": ["-m", "word_comment_management.main"],
      "cwd": "/path/to/python/word评论管理"
    }
  }
}
```

## 使用示例

根据具体工具功能使用相应的命令。
