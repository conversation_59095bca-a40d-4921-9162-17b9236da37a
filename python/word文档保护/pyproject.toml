[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-document-protection-mcp"
version = "1.0.0"
description = "Word文档保护MCP服务 - 提供文档保护和取消保护功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
    "lxml>=4.9.0",
    "msoffcrypto-tool>=5.0.0",
]

[project.scripts]
word-document-protection = "word_document_protection.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_document_protection*"]
