"""
Word文档保护工具 - 提供文档保护和取消保护功能
"""
import os
import hashlib
import io
from typing import Optional

from .utils import (
    check_file_writeable, 
    ensure_docx_extension,
    add_protection_info,
    remove_protection_info,
    verify_document_protection
)


async def protect_document(filename: str, password: str) -> str:
    """
    为Word文档添加密码保护
    
    Args:
        filename: Word文档路径
        password: 用于保护文档的密码
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法保护文档: {error_message}"
    
    try:
        # 尝试使用python-docx进行文档级别的保护
        try:
            from docx import Document
            from docx.oxml.shared import qn

            # 读取文档
            doc = Document(filename)

            # 为文档添加密码保护设置
            # 注意：python-docx不支持真正的加密，这只是文档级别的保护标记
            settings = doc.settings
            if hasattr(settings, 'element'):
                # 添加文档保护元素
                protection_elem = settings.element.find(qn('w:documentProtection'))
                if protection_elem is None:
                    from docx.oxml import OxmlElement
                    protection_elem = OxmlElement('w:documentProtection')
                    settings.element.append(protection_elem)

                # 设置保护类型和密码哈希
                protection_elem.set(qn('w:edit'), 'readOnly')
                protection_elem.set(qn('w:enforcement'), '1')
                # 使用简单的哈希作为密码验证
                password_hash = hashlib.sha256(password.encode()).hexdigest()[:8]
                protection_elem.set(qn('w:cryptProviderType'), 'rsaAES')
                protection_elem.set(qn('w:cryptAlgorithmClass'), 'hash')
                protection_elem.set(qn('w:cryptAlgorithmType'), 'typeAny')
                protection_elem.set(qn('w:cryptAlgorithmSid'), '14')
                protection_elem.set(qn('w:cryptSpinCount'), '100000')
                protection_elem.set(qn('w:hash'), password_hash)

            # 保存文档
            doc.save(filename)

            # 添加保护信息到元数据
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            success = add_protection_info(
                filename,
                protection_type="document_protection",
                password_hash=password_hash,
                raw_password=password
            )

            if success:
                return f"文档 {filename} 已添加文档级别保护（注意：这不是文件级加密，需要Office软件支持真正的密码保护）。"
            else:
                return f"文档 {filename} 保护设置成功，但保存保护信息失败。"

        except ImportError:
            # 如果python-docx不可用，只保存保护元数据
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            success = add_protection_info(
                filename,
                protection_type="password",
                password_hash=password_hash
            )

            if success:
                return f"文档 {filename} 保护信息已保存（注意：需要Office软件进行真正的密码保护）。"
            else:
                return f"保护文档 {filename} 失败。"
    
    except Exception as e:
        # 尝试在失败时恢复原始文件内容
        try:
            if 'original_data' in locals():
                with open(filename, "wb") as outfile:
                    outfile.write(original_data)
                return f"加密文档 {filename} 失败: {str(e)}。原始文件已恢复。"
            else:
                return f"加密文档 {filename} 失败: {str(e)}。无法恢复原始文件。"
        except Exception as restore_e:
            return f"加密文档 {filename} 失败: {str(e)}。恢复原始文件也失败: {str(restore_e)}"


async def unprotect_document(filename: str, password: str) -> str:
    """
    从Word文档中移除密码保护
    
    Args:
        filename: Word文档路径
        password: 用于保护文档的密码
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}"
    
    try:
        # 首先验证文档是否受保护以及密码是否正确
        is_verified, message = verify_document_protection(filename, password)
        if not is_verified:
            return f"取消保护失败: {message}"
        
        # 尝试移除文档级别的保护
        try:
            from docx import Document
            from docx.oxml.shared import qn

            # 检查文件是否是真正的加密文件（使用msoffcrypto检测）
            try:
                import msoffcrypto
                with open(filename, "rb") as infile:
                    file_data = infile.read()

                # 尝试检测是否是加密文件
                office_file = msoffcrypto.OfficeFile(io.BytesIO(file_data))
                if office_file.is_encrypted():
                    # 如果是真正的加密文件，尝试解密
                    office_file.load_key(password=password)
                    decrypted_data_io = io.BytesIO()
                    office_file.decrypt(outfile=decrypted_data_io)

                    # 用解密数据覆盖原始文件
                    with open(filename, "wb") as outfile:
                        outfile.write(decrypted_data_io.getvalue())

                    # 移除保护信息
                    success, remove_message = remove_protection_info(filename, password)

                    if success:
                        return f"文档 {filename} 解密成功。"
                    else:
                        return f"文档 {filename} 解密成功，但移除保护信息失败: {remove_message}"
            except:
                pass  # 不是加密文件，继续处理文档级别保护

            # 处理文档级别的保护
            doc = Document(filename)
            settings = doc.settings

            if hasattr(settings, 'element'):
                # 移除文档保护元素
                protection_elem = settings.element.find(qn('w:documentProtection'))
                if protection_elem is not None:
                    settings.element.remove(protection_elem)
                    doc.save(filename)

            # 移除保护信息
            success, remove_message = remove_protection_info(filename, password)

            if success:
                return f"文档 {filename} 保护已移除。"
            else:
                return f"移除文档 {filename} 保护失败: {remove_message}"

        except ImportError:
            # 如果python-docx不可用，只移除保护元数据
            success, remove_message = remove_protection_info(filename, password)

            if success:
                return f"文档 {filename} 保护信息已移除（注意：需要Office软件支持完整的保护移除）。"
            else:
                return f"移除文档 {filename} 保护失败: {remove_message}"
    
    except Exception as e:
        # 检查特定的msoffcrypto异常
        error_message = str(e)
        if "InvalidKeyError" in error_message or "incorrect password" in error_message.lower():
            return f"解密文档 {filename} 失败: 密码错误。"
        elif "InvalidFormatError" in error_message or "not encrypted" in error_message.lower():
            return f"解密文档 {filename} 失败: 文件未加密或不是支持的Office格式。"
        else:
            # 尝试在失败时恢复加密文件内容
            try:
                if 'encrypted_data' in locals():
                    with open(filename, "wb") as outfile:
                        outfile.write(encrypted_data)
                    return f"解密文档 {filename} 失败: {str(e)}。加密文件已恢复。"
                else:
                    return f"解密文档 {filename} 失败: {str(e)}。无法恢复加密文件。"
            except Exception as restore_e:
                return f"解密文档 {filename} 失败: {str(e)}。恢复加密文件也失败: {str(restore_e)}"


async def verify_document(filename: str, password: Optional[str] = None) -> str:
    """
    验证文档保护和/或数字签名
    
    Args:
        filename: Word文档路径
        password: 可选的验证密码
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    try:
        # 验证文档保护
        is_verified, message = verify_document_protection(filename, password)
        
        if not is_verified and password:
            return f"文档验证失败: {message}"
        
        return message
    except Exception as e:
        return f"验证文档失败: {str(e)}"
