"""
Word文档保护工具 - 提供文档保护和取消保护功能
"""
import os
import hashlib
import io
from typing import Optional

from .utils import (
    check_file_writeable, 
    ensure_docx_extension,
    add_protection_info,
    remove_protection_info,
    verify_document_protection
)


async def protect_document(filename: str, password: str) -> str:
    """
    为Word文档添加密码保护
    
    Args:
        filename: Word文档路径
        password: 用于保护文档的密码
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法保护文档: {error_message}"
    
    try:
        # 尝试使用msoffcrypto进行真正的加密
        try:
            import msoffcrypto
            
            # 读取原始文件内容
            with open(filename, "rb") as infile:
                original_data = infile.read()
            
            # 从原始数据创建msoffcrypto文件对象
            file = msoffcrypto.OfficeFile(io.BytesIO(original_data))
            file.load_key(password=password)  # 设置加密密码
            
            # 将数据加密到内存缓冲区
            encrypted_data_io = io.BytesIO()
            file.encrypt(password=password, outfile=encrypted_data_io)
            
            # 用加密数据覆盖原始文件
            with open(filename, "wb") as outfile:
                outfile.write(encrypted_data_io.getvalue())
            
            # 清理现有的保护元数据
            base_path, _ = os.path.splitext(filename)
            metadata_path = f"{base_path}.protection"
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            # 添加保护信息
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            success = add_protection_info(
                filename,
                protection_type="password",
                password_hash=password_hash,
                raw_password=password
            )
            
            if success:
                return f"文档 {filename} 已成功使用密码加密。"
            else:
                return f"文档 {filename} 加密成功，但保存保护信息失败。"
        
        except ImportError:
            # 如果msoffcrypto不可用，只保存保护元数据
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            success = add_protection_info(
                filename,
                protection_type="password",
                password_hash=password_hash
            )
            
            if success:
                return f"文档 {filename} 保护信息已保存（注意：需要msoffcrypto库进行真正的加密）。"
            else:
                return f"保护文档 {filename} 失败。"
    
    except Exception as e:
        # 尝试在失败时恢复原始文件内容
        try:
            if 'original_data' in locals():
                with open(filename, "wb") as outfile:
                    outfile.write(original_data)
                return f"加密文档 {filename} 失败: {str(e)}。原始文件已恢复。"
            else:
                return f"加密文档 {filename} 失败: {str(e)}。无法恢复原始文件。"
        except Exception as restore_e:
            return f"加密文档 {filename} 失败: {str(e)}。恢复原始文件也失败: {str(restore_e)}"


async def unprotect_document(filename: str, password: str) -> str:
    """
    从Word文档中移除密码保护
    
    Args:
        filename: Word文档路径
        password: 用于保护文档的密码
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}"
    
    try:
        # 首先验证文档是否受保护以及密码是否正确
        is_verified, message = verify_document_protection(filename, password)
        if not is_verified:
            return f"取消保护失败: {message}"
        
        # 尝试使用msoffcrypto进行真正的解密
        try:
            import msoffcrypto
            
            # 读取加密文件内容
            with open(filename, "rb") as infile:
                encrypted_data = infile.read()
            
            # 从加密数据创建msoffcrypto文件对象
            file = msoffcrypto.OfficeFile(io.BytesIO(encrypted_data))
            file.load_key(password=password)  # 设置解密密码
            
            # 将数据解密到内存缓冲区
            decrypted_data_io = io.BytesIO()
            file.decrypt(outfile=decrypted_data_io)  # 将缓冲区作为'outfile'参数传递
            
            # 用解密数据覆盖原始文件
            with open(filename, "wb") as outfile:
                outfile.write(decrypted_data_io.getvalue())
            
            # 移除保护信息
            success, remove_message = remove_protection_info(filename, password)
            
            if success:
                return f"文档 {filename} 解密成功。"
            else:
                return f"文档 {filename} 解密成功，但移除保护信息失败: {remove_message}"
        
        except ImportError:
            # 如果msoffcrypto不可用，只移除保护元数据
            success, remove_message = remove_protection_info(filename, password)
            
            if success:
                return f"文档 {filename} 保护信息已移除（注意：需要msoffcrypto库进行真正的解密）。"
            else:
                return f"移除文档 {filename} 保护失败: {remove_message}"
    
    except Exception as e:
        # 检查特定的msoffcrypto异常
        error_message = str(e)
        if "InvalidKeyError" in error_message or "incorrect password" in error_message.lower():
            return f"解密文档 {filename} 失败: 密码错误。"
        elif "InvalidFormatError" in error_message or "not encrypted" in error_message.lower():
            return f"解密文档 {filename} 失败: 文件未加密或不是支持的Office格式。"
        else:
            # 尝试在失败时恢复加密文件内容
            try:
                if 'encrypted_data' in locals():
                    with open(filename, "wb") as outfile:
                        outfile.write(encrypted_data)
                    return f"解密文档 {filename} 失败: {str(e)}。加密文件已恢复。"
                else:
                    return f"解密文档 {filename} 失败: {str(e)}。无法恢复加密文件。"
            except Exception as restore_e:
                return f"解密文档 {filename} 失败: {str(e)}。恢复加密文件也失败: {str(restore_e)}"


async def verify_document(filename: str, password: Optional[str] = None) -> str:
    """
    验证文档保护和/或数字签名
    
    Args:
        filename: Word文档路径
        password: 可选的验证密码
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    try:
        # 验证文档保护
        is_verified, message = verify_document_protection(filename, password)
        
        if not is_verified and password:
            return f"文档验证失败: {message}"
        
        return message
    except Exception as e:
        return f"验证文档失败: {str(e)}"
