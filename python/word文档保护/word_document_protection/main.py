"""
word文档保护 MCP服务主程序

Word文档保护MCP服务 - 提供文档保护和取消保护功能
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    protect_document,
    unprotect_document,
    verify_document
)

def get_transport_config():
    """从环境变量获取传输配置"""
    config = {
        'transport': 'stdio',
        'host': '0.0.0.0',
        'port': 8003,
        'path': '/mcp',
        'sse_path': '/sse'
    }
    
    transport = os.getenv('MCP_TRANSPORT', 'stdio').lower()
    print(f"传输方式: {transport}")
    
    valid_transports = ['stdio', 'streamable-http', 'sse']
    if transport not in valid_transports:
        print(f"警告: 无效的传输方式 '{transport}'。回退到 'stdio'。")
        transport = 'stdio'
    
    config['transport'] = transport
    config['host'] = os.getenv('MCP_HOST', config['host'])
    config['port'] = int(os.getenv('MCP_PORT', config['port']))
    config['path'] = os.getenv('MCP_PATH', config['path'])
    config['sse_path'] = os.getenv('MCP_SSE_PATH', config['sse_path'])
    
    return config

# 初始化FastMCP服务器
mcp = FastMCP("word文档保护服务")

def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    def protect_document_tool(filename: str, password: str):
        """为Word文档添加密码保护"""
        return protect_document(filename, password)

    @mcp.tool()
    def unprotect_document_tool(filename: str, password: str):
        """从Word文档中移除密码保护"""
        return unprotect_document(filename, password)

    @mcp.tool()
    def verify_document_tool(filename: str, password: str = None):
        """验证文档保护和/或数字签名"""
        return verify_document(filename, password)

def run_server():
    """运行word文档保护MCP服务器"""
    config = get_transport_config()
    register_tools()
    
    transport_type = config['transport']
    print(f"启动word文档保护MCP服务器，使用 {transport_type} 传输方式...")
    
    try:
        if transport_type == 'stdio':
            print("服务器运行在stdio传输方式")
            mcp.run(transport='stdio')
        elif transport_type == 'streamable-http':
            print(f"服务器运行在streamable-http传输方式，地址: http://{config['host']}:{config['port']}{config['path']}")
            mcp.run(transport='streamable-http', host=config['host'], port=config['port'], path=config['path'])
        elif transport_type == 'sse':
            print(f"服务器运行在SSE传输方式，地址: http://{config['host']}:{config['port']}{config['sse_path']}")
            mcp.run(transport='sse', host=config['host'], port=config['port'], path=config['sse_path'])
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)
    
    return mcp

def main():
    """服务器的主入口点"""
    run_server()

if __name__ == "__main__":
    main()
