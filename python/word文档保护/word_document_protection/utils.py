"""
文档保护工具类 - 提供文件操作和文档保护的基础功能
"""
import os
import json
import hashlib
import datetime
import io
import tempfile
import shutil
from typing import Dict, List, Any, Tuple, Optional


def check_file_writeable(filepath: str) -> Tuple[bool, str]:
    """
    检查文件是否可写
    
    Args:
        filepath: 文件路径
        
    Returns:
        Tuple of (is_writeable, error_message)
    """
    # 如果文件不存在，检查目录是否可写
    if not os.path.exists(filepath):
        directory = os.path.dirname(filepath)
        # 如果没有指定目录，使用当前目录
        if directory == '':
            directory = '.'
        if not os.path.exists(directory):
            return False, f"目录 {directory} 不存在"
        if not os.access(directory, os.W_OK):
            return False, f"目录 {directory} 不可写"
        return True, ""
    
    # 如果文件存在，检查是否可写
    if not os.access(filepath, os.W_OK):
        return False, f"文件 {filepath} 不可写（权限被拒绝）"
    
    # 尝试打开文件进行写入，检查是否被锁定
    try:
        with open(filepath, 'a'):
            pass
        return True, ""
    except IOError as e:
        return False, f"文件 {filepath} 不可写: {str(e)}"
    except Exception as e:
        return False, f"检查文件权限时出现未知错误: {str(e)}"


def ensure_docx_extension(filename: str) -> str:
    """
    确保文件名具有.docx扩展名
    
    Args:
        filename: 要检查的文件名
        
    Returns:
        带有.docx扩展名的文件名
    """
    if not filename.endswith('.docx'):
        return filename + '.docx'
    return filename


def add_protection_info(doc_path: str, protection_type: str, password_hash: str, 
                       sections: Optional[List[str]] = None, 
                       signature_info: Optional[Dict[str, Any]] = None,
                       raw_password: Optional[str] = None) -> bool:
    """
    添加文档保护信息到单独的元数据文件并加密文档
    
    Args:
        doc_path: 文档路径
        protection_type: 保护类型 ('password', 'restricted', 'signature')
        password_hash: 哈希密码用于安全
        sections: 可编辑的部分名称列表（用于受限编辑）
        signature_info: 数字签名信息
        raw_password: 用于文档加密的实际密码
        
    Returns:
        如果保护信息成功添加返回True，否则返回False
    """
    # 基于文档路径创建元数据文件名
    base_path, _ = os.path.splitext(doc_path)
    metadata_path = f"{base_path}.protection"
    
    # 准备保护数据
    protection_data = {
        "type": protection_type,
        "password_hash": password_hash,
        "applied_date": datetime.datetime.now().isoformat(),
    }
    
    if sections:
        protection_data["editable_sections"] = sections
        
    if signature_info:
        protection_data["signature"] = signature_info
    
    # 将保护信息写入元数据文件
    try:
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(protection_data, f, indent=2, ensure_ascii=False)
        
        # 如果提供了raw_password，应用实际的文档加密
        if protection_type == "password" and raw_password:
            try:
                import msoffcrypto
                
                # 为加密输出创建临时文件
                temp_fd, temp_path = tempfile.mkstemp(suffix='.docx')
                os.close(temp_fd)
                
                try:
                    # 打开文档
                    with open(doc_path, 'rb') as f:
                        office_file = msoffcrypto.OfficeFile(f)
                        
                        # 使用密码加密
                        office_file.load_key(password=raw_password)
                        
                        # 将加密文件写入临时路径
                        with open(temp_path, 'wb') as out_file:
                            office_file.encrypt(out_file)
                    
                    # 用加密版本替换原文件
                    shutil.move(temp_path, doc_path)
                    
                    # 更新元数据以注明应用了真正的加密
                    protection_data["true_encryption"] = True
                    with open(metadata_path, 'w', encoding='utf-8') as f:
                        json.dump(protection_data, f, indent=2, ensure_ascii=False)
                        
                except Exception as e:
                    print(f"加密错误: {str(e)}")
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                    return False
            except ImportError:
                # 如果msoffcrypto不可用，只保存元数据
                print("警告: msoffcrypto库不可用，只保存保护元数据")
        
        return True
    except Exception as e:
        print(f"保护错误: {str(e)}")
        return False


def remove_protection_info(filename: str, password: Optional[str] = None) -> Tuple[bool, str]:
    """
    从文档中移除保护信息并在必要时解密
    
    Args:
        filename: Word文档路径
        password: 移除保护前验证的密码
        
    Returns:
        Tuple of (success, message)
    """
    base_path, _ = os.path.splitext(filename)
    metadata_path = f"{base_path}.protection"
    
    # 检查保护元数据是否存在
    if not os.path.exists(metadata_path):
        return False, "文档未受保护"
    
    try:
        # 加载保护数据
        with open(metadata_path, 'r', encoding='utf-8') as f:
            protection_data = json.load(f)
        
        # 如果提供了密码且需要验证，则验证密码
        if password and protection_data.get("password_hash"):
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            if password_hash != protection_data.get("password_hash"):
                return False, "密码错误"
        
        # 如果应用了真正的加密且提供了密码，则处理
        if protection_data.get("true_encryption") and password:
            try:
                import msoffcrypto
                
                # 为解密输出创建临时文件
                temp_fd, temp_path = tempfile.mkstemp(suffix='.docx')
                os.close(temp_fd)
                
                try:
                    # 打开加密文档
                    with open(filename, 'rb') as f:
                        office_file = msoffcrypto.OfficeFile(f)
                        office_file.load_key(password=password)
                        
                        # 解密到临时文件
                        with open(temp_path, 'wb') as out_file:
                            office_file.decrypt(out_file)
                    
                    # 用解密版本替换原文件
                    shutil.move(temp_path, filename)
                    
                except Exception as e:
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                    return False, f"解密失败: {str(e)}"
            except ImportError:
                return False, "msoffcrypto库不可用，无法解密文档"
        
        # 移除保护元数据文件
        os.remove(metadata_path)
        
        return True, "文档保护已成功移除"
    except Exception as e:
        return False, f"移除保护失败: {str(e)}"


def verify_document_protection(filename: str, password: Optional[str] = None) -> Tuple[bool, str]:
    """
    验证文档保护和/或数字签名
    
    Args:
        filename: Word文档路径
        password: 可选的验证密码
        
    Returns:
        Tuple of (is_verified, message)
    """
    base_path, _ = os.path.splitext(filename)
    metadata_path = f"{base_path}.protection"
    
    if not os.path.exists(metadata_path):
        return True, "文档未受保护"
    
    try:
        with open(metadata_path, 'r', encoding='utf-8') as f:
            protection_data = json.load(f)
        
        protection_type = protection_data.get("type")
        
        if protection_type == "password":
            if password:
                password_hash = hashlib.sha256(password.encode()).hexdigest()
                if password_hash == protection_data.get("password_hash"):
                    return True, "密码验证成功"
                else:
                    return False, "密码错误"
            else:
                return False, "需要密码验证"
        
        elif protection_type == "signature":
            # 验证数字签名的完整性
            signature_info = protection_data.get("signature", {})
            return True, f"文档由 {signature_info.get('signer', '未知')} 签名"
        
        elif protection_type == "restricted":
            sections = protection_data.get("editable_sections", [])
            return True, f"文档受限编辑，可编辑部分: {', '.join(sections)}"
        
        return True, f"文档受 {protection_type} 保护"
    except Exception as e:
        return False, f"验证保护失败: {str(e)}"
