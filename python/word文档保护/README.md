# word文档保护 MCP服务

Word文档保护MCP服务 - 提供文档保护和取消保护功能

## 功能特性

### 🔧 工具列表 (2个)
- `protect_document - 为Word文档添加密码保护`
- `unprotect_document - 移除Word文档的密码保护`

## 安装和配置

### 本地安装
```bash
cd python/word文档保护
pip install -e .
```

### Claude Desktop配置
```json
{
  "mcpServers": {
    "word-document-protection": {
      "command": "python",
      "args": ["-m", "word_document_protection.main"],
      "cwd": "/path/to/python/word文档保护"
    }
  }
}
```

## 使用示例

根据具体工具功能使用相应的命令。
