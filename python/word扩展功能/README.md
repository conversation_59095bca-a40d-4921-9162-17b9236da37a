# word扩展功能 MCP服务

Word扩展功能MCP服务 - 提供PDF转换、文本查找等扩展功能

## 功能特性

### 🔧 工具列表 (3个)
- `get_paragraph_text_from_document - 获取特定段落文本`
- `find_text_in_document - 在文档中查找文本`
- `convert_to_pdf - 将Word文档转换为PDF`

## 安装和配置

### 本地安装
```bash
cd python/word扩展功能
pip install -e .
```

### Claude Desktop配置
```json
{
  "mcpServers": {
    "word-extended-features": {
      "command": "python",
      "args": ["-m", "word_extended_features.main"],
      "cwd": "/path/to/python/word扩展功能"
    }
  }
}
```

## 使用示例

根据具体工具功能使用相应的命令。
