[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-extended-features-mcp"
version = "1.0.0"
description = "Word扩展功能MCP服务 - 提供PDF转换、文本查找等扩展功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
    "lxml>=4.9.0",
]

[project.scripts]
word-extended-features = "word_extended_features.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_extended_features*"]
