"""
内容编辑工具类 - 提供文件操作和文档内容编辑的基础功能
"""
import os
import shutil
from typing import Dict, List, Any, Tuple, Optional
from docx import Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.oxml.ns import qn


def check_file_writeable(filepath: str) -> Tuple[bool, str]:
    """
    检查文件是否可写
    
    Args:
        filepath: 文件路径
        
    Returns:
        Tuple of (is_writeable, error_message)
    """
    # 如果文件不存在，检查目录是否可写
    if not os.path.exists(filepath):
        directory = os.path.dirname(filepath)
        # 如果没有指定目录，使用当前目录
        if directory == '':
            directory = '.'
        if not os.path.exists(directory):
            return False, f"目录 {directory} 不存在"
        if not os.access(directory, os.W_OK):
            return False, f"目录 {directory} 不可写"
        return True, ""
    
    # 如果文件存在，检查是否可写
    if not os.access(filepath, os.W_OK):
        return False, f"文件 {filepath} 不可写（权限被拒绝）"
    
    # 尝试打开文件进行写入，检查是否被锁定
    try:
        with open(filepath, 'a'):
            pass
        return True, ""
    except IOError as e:
        return False, f"文件 {filepath} 不可写: {str(e)}"
    except Exception as e:
        return False, f"检查文件权限时出现未知错误: {str(e)}"


def ensure_docx_extension(filename: str) -> str:
    """
    确保文件名具有.docx扩展名
    
    Args:
        filename: 要检查的文件名
        
    Returns:
        带有.docx扩展名的文件名
    """
    if not filename.endswith('.docx'):
        return filename + '.docx'
    return filename


def find_and_replace_text(doc, old_text, new_text):
    """
    在整个文档中查找并替换文本，跳过目录(TOC)段落
    
    Args:
        doc: Document对象
        old_text: 要查找的文本
        new_text: 要替换的文本
        
    Returns:
        替换的次数
    """
    count = 0
    
    # 在段落中搜索
    for para in doc.paragraphs:
        # 跳过TOC段落
        if para.style and para.style.name.startswith("TOC"):
            continue
        if old_text in para.text:
            for run in para.runs:
                if old_text in run.text:
                    run.text = run.text.replace(old_text, new_text)
                    count += 1
    
    # 在表格中搜索
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for para in cell.paragraphs:
                    # 跳过表格中的TOC段落
                    if para.style and para.style.name.startswith("TOC"):
                        continue
                    if old_text in para.text:
                        for run in para.runs:
                            if old_text in run.text:
                                run.text = run.text.replace(old_text, new_text)
                                count += 1
    
    return count


def ensure_heading_style(doc):
    """确保文档中存在标题样式"""
    try:
        # 尝试访问标题样式，如果不存在会抛出异常
        for level in range(1, 10):
            style_name = f'Heading {level}'
            try:
                doc.styles[style_name]
            except KeyError:
                # 如果样式不存在，创建基本样式
                pass
    except Exception:
        # 如果出现任何问题，忽略并继续
        pass


def ensure_table_style(doc):
    """确保文档中存在表格样式"""
    try:
        # 尝试访问表格样式
        doc.styles['Table Grid']
    except KeyError:
        # 如果样式不存在，忽略
        pass
    except Exception:
        # 如果出现任何问题，忽略并继续
        pass


def insert_header_near_text(doc_path: str, target_text: str = None, header_title: str = "",
                           position: str = 'after', header_style: str = 'Heading 1',
                           target_paragraph_index: int = None) -> str:
    """在指定段落前后插入标题（按文本或段落索引指定）。在文本搜索中跳过TOC段落。"""
    if not os.path.exists(doc_path):
        return f"文档 {doc_path} 不存在"
    try:
        doc = Document(doc_path)
        found = False
        para = None
        if target_paragraph_index is not None:
            if target_paragraph_index < 0 or target_paragraph_index >= len(doc.paragraphs):
                return f"无效的target_paragraph_index: {target_paragraph_index}。文档有 {len(doc.paragraphs)} 个段落。"
            para = doc.paragraphs[target_paragraph_index]
            found = True
        else:
            for i, p in enumerate(doc.paragraphs):
                # 跳过TOC段落
                if p.style and p.style.name.lower().startswith("toc"):
                    continue
                if target_text and target_text in p.text:
                    para = p
                    found = True
                    break
        if not found or para is None:
            return f"未找到目标段落（按索引或文本）。（文本搜索中跳过TOC段落）"

        # 保存锚点索引
        if target_paragraph_index is not None:
            anchor_index = target_paragraph_index
        else:
            anchor_index = None
            for i, p in enumerate(doc.paragraphs):
                if p is para:
                    anchor_index = i
                    break

        new_para = doc.add_paragraph(header_title, style=header_style)
        if position == 'before':
            para._element.addprevious(new_para._element)
        else:
            para._element.addnext(new_para._element)
        doc.save(doc_path)

        if anchor_index is not None:
            return f"标题 '{header_title}' (样式: {header_style}) 已插入到段落 {position} (索引 {anchor_index})。"
        else:
            return f"标题 '{header_title}' (样式: {header_style}) 已插入到目标段落 {position}。"
    except Exception as e:
        return f"插入标题失败: {str(e)}"


def insert_line_or_paragraph_near_text(doc_path: str, target_text: str = None, line_text: str = "",
                                      position: str = 'after', line_style: str = None,
                                      target_paragraph_index: int = None) -> str:
    """
    在目标段落前后插入新行或段落（使用指定样式或匹配样式）。
    可以通过文本（第一个匹配）或段落索引指定目标。
    如果使用文本搜索，跳过样式名以'TOC'开头的段落。
    """
    if not os.path.exists(doc_path):
        return f"文档 {doc_path} 不存在"
    try:
        doc = Document(doc_path)
        found = False
        para = None
        if target_paragraph_index is not None:
            if target_paragraph_index < 0 or target_paragraph_index >= len(doc.paragraphs):
                return f"无效的target_paragraph_index: {target_paragraph_index}。文档有 {len(doc.paragraphs)} 个段落。"
            para = doc.paragraphs[target_paragraph_index]
            found = True
        else:
            for i, p in enumerate(doc.paragraphs):
                # 跳过TOC段落
                if p.style and p.style.name.lower().startswith("toc"):
                    continue
                if target_text and target_text in p.text:
                    para = p
                    found = True
                    break
        if not found or para is None:
            return f"未找到目标段落（按索引或文本）。（文本搜索中跳过TOC段落）"

        # 保存锚点索引
        if target_paragraph_index is not None:
            anchor_index = target_paragraph_index
        else:
            anchor_index = None
            for i, p in enumerate(doc.paragraphs):
                if p is para:
                    anchor_index = i
                    break

        # 确定样式：使用提供的或匹配目标
        style = line_style if line_style else para.style
        new_para = doc.add_paragraph(line_text, style=style)
        if position == 'before':
            para._element.addprevious(new_para._element)
        else:
            para._element.addnext(new_para._element)
        doc.save(doc_path)

        if anchor_index is not None:
            return f"行/段落已插入到段落 {position} (索引 {anchor_index})，样式为 '{style}'。"
        else:
            return f"行/段落已插入到目标段落 {position}，样式为 '{style}'。"
    except Exception as e:
        return f"插入行/段落失败: {str(e)}"


def insert_numbered_list_near_text(doc_path: str, target_text: str = None, list_items: list = None,
                                  position: str = 'after', target_paragraph_index: int = None) -> str:
    """
    在目标段落前后插入编号列表。通过文本或段落索引指定。在文本搜索中跳过TOC段落。
    Args:
        doc_path: Word文档路径
        target_text: 在段落中搜索的文本（如果使用索引则可选）
        list_items: 字符串列表，每个作为列表项
        position: 'before' 或 'after'（默认：'after'）
        target_paragraph_index: 可选的段落索引用作锚点
    Returns:
        状态消息
    """
    if not os.path.exists(doc_path):
        return f"文档 {doc_path} 不存在"
    try:
        doc = Document(doc_path)
        found = False
        para = None
        if target_paragraph_index is not None:
            if target_paragraph_index < 0 or target_paragraph_index >= len(doc.paragraphs):
                return f"无效的target_paragraph_index: {target_paragraph_index}。文档有 {len(doc.paragraphs)} 个段落。"
            para = doc.paragraphs[target_paragraph_index]
            found = True
        else:
            for i, p in enumerate(doc.paragraphs):
                # 跳过TOC段落
                if p.style and p.style.name.lower().startswith("toc"):
                    continue
                if target_text and target_text in p.text:
                    para = p
                    found = True
                    break
        if not found or para is None:
            return f"未找到目标段落（按索引或文本）。（文本搜索中跳过TOC段落）"

        # 保存锚点索引
        if target_paragraph_index is not None:
            anchor_index = target_paragraph_index
        else:
            anchor_index = None
            for i, p in enumerate(doc.paragraphs):
                if p is para:
                    anchor_index = i
                    break

        # 编号列表的健壮样式选择
        style_name = None
        for candidate in ['List Number', 'List Paragraph', 'Normal']:
            try:
                _ = doc.styles[candidate]
                style_name = candidate
                break
            except KeyError:
                continue
        if not style_name:
            style_name = None  # 回退到默认

        new_paras = []
        for item in (list_items or []):
            p = doc.add_paragraph(item, style=style_name)
            new_paras.append(p)

        # 将新段落移动到正确位置
        for p in reversed(new_paras):
            if position == 'before':
                para._element.addprevious(p._element)
            else:
                para._element.addnext(p._element)
        doc.save(doc_path)

        if anchor_index is not None:
            return f"编号列表已插入到段落 {position} (索引 {anchor_index})。"
        else:
            return f"编号列表已插入到目标段落 {position}。"
    except Exception as e:
        return f"插入编号列表失败: {str(e)}"
