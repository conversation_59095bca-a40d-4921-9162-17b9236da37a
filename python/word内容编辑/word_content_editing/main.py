"""
Word内容编辑 MCP服务主程序

提供Word文档的内容编辑功能，包括段落、标题、表格、图片等内容的添加和编辑。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    add_paragraph,
    add_heading,
    add_table,
    add_page_break,
    delete_paragraph,
    search_and_replace,
    insert_line_or_paragraph_near_text_tool
)


def get_transport_config():
    """
    从环境变量获取传输配置
    
    Returns:
        dict: 包含传输类型、主机、端口等设置的传输配置
    """
    # 默认配置
    config = {
        'transport': 'stdio',  # 默认使用stdio以保持向后兼容
        'host': '0.0.0.0',
        'port': 8002,
        'path': '/mcp',
        'sse_path': '/sse'
    }
    
    # 使用环境变量覆盖默认配置
    transport = os.getenv('MCP_TRANSPORT', 'stdio').lower()
    print(f"传输方式: {transport}")
    
    # 验证传输类型
    valid_transports = ['stdio', 'streamable-http', 'sse']
    if transport not in valid_transports:
        print(f"警告: 无效的传输方式 '{transport}'。回退到 'stdio'。")
        transport = 'stdio'
    
    config['transport'] = transport
    config['host'] = os.getenv('MCP_HOST', config['host'])
    config['port'] = int(os.getenv('MCP_PORT', config['port']))
    config['path'] = os.getenv('MCP_PATH', config['path'])
    config['sse_path'] = os.getenv('MCP_SSE_PATH', config['sse_path'])
    
    return config


# 初始化FastMCP服务器
mcp = FastMCP("Word内容编辑服务")


def register_tools():
    """使用FastMCP装饰器注册所有工具"""
    
    @mcp.tool()
    def add_paragraph_tool(filename: str, text: str, style: str = None):
        """向Word文档添加段落"""
        return add_paragraph(filename, text, style)
    
    @mcp.tool()
    def add_heading_tool(filename: str, text: str, level: int = 1):
        """向Word文档添加标题"""
        return add_heading(filename, text, level)
    
    @mcp.tool()
    def add_table_tool(filename: str, rows: int, cols: int, data: list = None):
        """向Word文档添加表格"""
        return add_table(filename, rows, cols, data)
    
    @mcp.tool()
    def add_page_break_tool(filename: str):
        """向文档添加分页符"""
        return add_page_break(filename)

    @mcp.tool()
    def delete_paragraph_tool(filename: str, paragraph_index: int):
        """从文档中删除段落"""
        return delete_paragraph(filename, paragraph_index)

    @mcp.tool()
    def search_and_replace_tool(filename: str, find_text: str, replace_text: str):
        """搜索文本并替换所有出现的地方"""
        return search_and_replace(filename, find_text, replace_text)

    @mcp.tool()
    def insert_line_or_paragraph_near_text(filename: str, target_text: str = None, line_text: str = None,
                                          position: str = 'after', line_style: str = None,
                                          target_paragraph_index: int = None):
        """在指定文本附近插入新行或段落"""
        return insert_line_or_paragraph_near_text_tool(filename, target_text, line_text, position, line_style, target_paragraph_index)


def run_server():
    """运行Word内容编辑MCP服务器，支持可配置的传输方式"""
    # 获取传输配置
    config = get_transport_config()
    
    # 注册所有工具
    register_tools()
    
    # 打印启动信息
    transport_type = config['transport']
    print(f"启动Word内容编辑MCP服务器，使用 {transport_type} 传输方式...")
    
    try:
        if transport_type == 'stdio':
            # 使用stdio传输运行（默认，向后兼容）
            print("服务器运行在stdio传输方式")
            mcp.run(transport='stdio')
            
        elif transport_type == 'streamable-http':
            # 使用streamable HTTP传输运行
            print(f"服务器运行在streamable-http传输方式，地址: http://{config['host']}:{config['port']}{config['path']}")
            mcp.run(
                transport='streamable-http',
                host=config['host'],
                port=config['port'],
                path=config['path']
            )
            
        elif transport_type == 'sse':
            # 使用SSE传输运行
            print(f"服务器运行在SSE传输方式，地址: http://{config['host']}:{config['port']}{config['sse_path']}")
            mcp.run(
                transport='sse',
                host=config['host'],
                port=config['port'],
                path=config['sse_path']
            )
            
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)
    
    return mcp


def main():
    """服务器的主入口点"""
    run_server()


if __name__ == "__main__":
    main()
